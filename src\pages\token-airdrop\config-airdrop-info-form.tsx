import { DateTimePickerField, NumberInputField } from '@/components/core';
import { MAX_NUMBER_INPUT } from '@/constants/common';
import { AirdropSchemaFormType } from '@/schema/airdrop';
import { Controller, useFormContext } from 'react-hook-form';
import ImportAirdropAddressSection from './import-airdrop-address-section';
import clsx from 'clsx';
import TokenSelector from './token-selector';

export default function ConfigAirdropInfoForm() {
  const { control, watch, setValue } = useFormContext<AirdropSchemaFormType>();

  return (
    <div className="w-full flex flex-col gap-5">
      <div className="relative">
        <Controller
          name="airdropQuantity"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <>
              <NumberInputField
                label="Airdrop per Person"
                numberValue={Number(field.value)}
                onChangeValue={(e) => field.onChange(Number(e))}
                error={error?.message}
                maxNumbers={MAX_NUMBER_INPUT}
                maxDigits={10}
                inputStyle="!pr-28"
              />
              <TokenSelector
                value={watch('tokenAirdrop')}
                onChange={(value) => setValue('tokenAirdrop', value)}
                className={clsx(
                  'absolute right-3 w-fit',
                  error?.message ? 'top-1/2 -translate-y-1/2' : 'bottom-2'
                )}
              />
            </>
          )}
        />
      </div>
      <Controller
        name="airdropStartAt"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <DateTimePickerField
            dateValue={field.value}
            onChangeValue={(date) => {
              field.onChange(date);
            }}
            label="Start At"
            error={error?.message}
            minDate={new Date()}
          />
        )}
      />
      <ImportAirdropAddressSection />
    </div>
  );
}
